package local

import (
	"fmt"
	"strings"
	"testing"

	"github.com/breezewish/pantheon-backend/internal/config"
	"github.com/breezewish/pantheon-backend/internal/provider"
	"github.com/stretchr/testify/require"
)

// TestMinimalIsolation tests the very minimal aspect of snapshot isolation
func TestMinimalIsolation(t *testing.T) {
	tempDir := t.TempDir()
	config := config.NewConfig()
	config.Storage.DataDir = tempDir

	// Get a local p
	p, err := NewLocalProvider(config)
	ph := provider.ProviderHelper{Provider: p}
	require.NoError(t, err)

	// Use a base image that should be available
	baseImgToken := "busybox:1.37.0"

	// Execute a command to create a file in the base image
	fmt.Println("Creating file in base image...")
	writeCmd := []string{"sh", "-c", "echo 'original' > /file.txt"}
	writeOutput, err := ph.Exec(provider.AsyncExecOpts{SrcSnapToken: baseImgToken, Command: writeCmd})
	require.NoError(t, err)
	snapA := writeOutput.NewSnapToken
	defer func() {
		p.Remove(snapA)
	}()
	fmt.Printf("Created snapshot A: %s\n", snapA)

	// Read file contents from snapshot A
	fmt.Println("Reading file from snapshot A...")
	readCmdA := []string{"cat", "/file.txt"}
	readOutputA, err := ph.ExecReadOnly(provider.AsyncExecOpts{SrcSnapToken: snapA, Command: readCmdA})
	require.NoError(t, err)
	contentA := readOutputA.Stdout
	require.NoError(t, err)

	// Create a modified version in snapshot B
	fmt.Println("Creating modified file in snapshot B...")
	modifyCmd := []string{"sh", "-c", "echo 'modified' > /file.txt"}
	modifyOutput, err := ph.Exec(provider.AsyncExecOpts{SrcSnapToken: snapA, Command: modifyCmd})
	require.NoError(t, err)
	snapB := modifyOutput.NewSnapToken
	defer func() {
		p.Remove(snapB)
	}()
	fmt.Printf("Created snapshot B: %s\n", snapB)

	// Read file contents from snapshot B
	fmt.Println("Reading file from snapshot B...")
	readCmdB := []string{"cat", "/file.txt"}
	readOutputB, err := ph.ExecReadOnly(provider.AsyncExecOpts{SrcSnapToken: snapB, Command: readCmdB})
	require.NoError(t, err)
	contentB := readOutputB.Stdout

	// Read file contents from snapshot A again to verify it's unchanged
	fmt.Println("Reading file from snapshot A again...")
	readOutputA2, err := ph.ExecReadOnly(provider.AsyncExecOpts{SrcSnapToken: snapA, Command: readCmdA})
	require.NoError(t, err)
	contentA2 := readOutputA2.Stdout

	// Compare and verify isolation
	strA := strings.TrimSpace(string(contentA))
	strB := strings.TrimSpace(string(contentB))
	strA2 := strings.TrimSpace(string(contentA2))

	fmt.Printf("Snapshot A (first read): '%s'\n", strA)
	fmt.Printf("Snapshot B: '%s'\n", strB)
	fmt.Printf("Snapshot A (second read): '%s'\n", strA2)

	require.Equal(t, "original", strA, "Snapshot A should have original content")
	require.Equal(t, "modified", strB, "Snapshot B should have modified content")
	require.Equal(t, strA, strA2, "Snapshot A should remain unchanged")
	require.NotEqual(t, strA, strB, "Content should differ between snapshots")
}

// TestLocalProviderBranching verifies that the snapshot isolation works correctly.
// This test verifies that:
// 1. We can create a file in snap A.
// 2. We can create snap B from A and modify the file.
// 3. The file in snap A remains unchanged.
// 4. We can create snap C from A (sibling of B) and the file from A is still visible and can be appended to.
func TestLocalProviderBranching(t *testing.T) {
	// Setup provider with a temporary directory
	tempDir := t.TempDir()
	cfg := config.NewConfig()
	cfg.Storage.DataDir = tempDir
	p, err := NewLocalProvider(cfg)
	require.NoError(t, err, "Failed to create local provider")
	ph := provider.ProviderHelper{Provider: p}

	baseImageToken := "busybox:1.37.0"

	// Snap A: Create a file in the base image
	createFileCmd := []string{"sh", "-c", "echo 'original content' > /test.txt"}
	snapAOutput, err := ph.Exec(provider.AsyncExecOpts{SrcSnapToken: baseImageToken, Command: createFileCmd})
	require.NoError(t, err, "Failed to execute command on base image")
	snapAToken := snapAOutput.NewSnapToken
	t.Logf("Created snap A with Token: %s", snapAToken)
	defer func() {
		p.Remove(snapAToken)
	}()

	// Verify file content in Snap A
	catCmd := []string{"cat", "/test.txt"}
	catOutputA, err := ph.ExecReadOnly(provider.AsyncExecOpts{SrcSnapToken: snapAToken, Command: catCmd})
	require.NoError(t, err, "Failed to cat file in snap A")
	t.Logf("Content in snap A: %s", catOutputA.Stdout)
	require.Contains(t, catOutputA.Stdout, "original content", "Snap A should contain the original content")

	// Snap B: Modify the file in a child of Snap A
	modifyFileCmd := []string{"sh", "-c", "echo 'modified content' > /test.txt"}
	snapBOutput, err := ph.Exec(provider.AsyncExecOpts{SrcSnapToken: snapAToken, Command: modifyFileCmd})
	require.NoError(t, err, "Failed to execute modify command on snap A")
	snapBToken := snapBOutput.NewSnapToken
	t.Logf("Created snap B with Token: %s", snapBToken)
	defer func() {
		p.Remove(snapBToken)
	}()

	// Verify file content in Snap B
	catOutputB, err := ph.ExecReadOnly(provider.AsyncExecOpts{SrcSnapToken: snapBToken, Command: catCmd})
	require.NoError(t, err, "Failed to cat file in snap B")
	t.Logf("Content in snap B: %s", catOutputB.Stdout)
	require.Contains(t, catOutputB.Stdout, "modified content", "Snap B should contain the modified content")

	// Verify file content in Snap A again (should be unchanged)
	catOutputA2, err := ph.ExecReadOnly(provider.AsyncExecOpts{SrcSnapToken: snapAToken, Command: catCmd})
	require.NoError(t, err, "Failed to cat file in snap A again")
	t.Logf("Content in snap A (after B was created): %s", catOutputA2.Stdout)
	require.Contains(t, catOutputA2.Stdout, "original content", "Snap A should still contain the original content")

	// Snap C: Create another child from Snap A (sibling of B)
	appendFileCmd := []string{"sh", "-c", "echo 'appended content' >> /test.txt"}
	snapCOutput, err := ph.Exec(provider.AsyncExecOpts{SrcSnapToken: snapAToken, Command: appendFileCmd})
	require.NoError(t, err, "Failed to execute append command on snap A")
	snapCToken := snapCOutput.NewSnapToken
	t.Logf("Created snap C with Token: %s", snapCToken)
	defer func() {
		p.Remove(snapCToken)
	}()

	// Verify file content in Snap C
	catOutputC, err := ph.ExecReadOnly(provider.AsyncExecOpts{SrcSnapToken: snapCToken, Command: catCmd})
	require.NoError(t, err, "Failed to cat file in snap C")
	t.Logf("Content in snap C: %s", catOutputC.Stdout)
	require.Contains(t, catOutputC.Stdout, "original content", "Snap C should contain the original content")
	require.Contains(t, catOutputC.Stdout, "appended content", "Snap C should contain the appended content")

	// Verify the content in Snap B is still intact (not affected by Snap C)
	catOutputB2, err := ph.ExecReadOnly(provider.AsyncExecOpts{SrcSnapToken: snapBToken, Command: catCmd})
	require.NoError(t, err, "Failed to cat file in snap B again")
	t.Logf("Content in snap B (after C was created): %s", catOutputB2.Stdout)
	require.Contains(t, catOutputB2.Stdout, "modified content", "Snap B should still contain the modified content")
	require.NotContains(t, catOutputB2.Stdout, "appended content", "Snap B should not contain content from Snap C")
}

// TestCrossBranchIsolation tests more complex isolation scenarios between sibling and child branches.
func TestCrossBranchIsolation(t *testing.T) {
	// Setup provider with a temporary directory
	tempDir := t.TempDir()
	cfg := config.NewConfig()
	cfg.Storage.DataDir = tempDir
	p, err := NewLocalProvider(cfg)
	require.NoError(t, err, "Failed to create local provider")
	ph := provider.ProviderHelper{Provider: p}

	// Import a base image
	baseImageToken := "busybox:1.37.0"

	// Create a directory structure in the base image
	setupCmd := []string{"sh", "-c", "mkdir -p /app && echo 'version 1' > /app/data.txt"}
	baseOutput, err := ph.Exec(provider.AsyncExecOpts{SrcSnapToken: baseImageToken, Command: setupCmd})
	require.NoError(t, err, "Failed to set up directory structure")
	baseSnapToken := baseOutput.NewSnapToken
	defer func() {
		p.Remove(baseSnapToken)
	}()

	// Branch 1: Update the file and add a new file
	branch1Cmd := []string{"sh", "-c", "echo 'version 2 - branch 1' > /app/data.txt && echo 'branch 1 file' > /app/branch1.txt"}
	branch1Output, err := ph.Exec(provider.AsyncExecOpts{SrcSnapToken: baseSnapToken, Command: branch1Cmd})
	require.NoError(t, err, "Failed to create branch 1")
	branch1Token := branch1Output.NewSnapToken
	defer func() {
		p.Remove(branch1Token)
	}()

	// Branch 2: Update the same file differently and add another new file
	branch2Cmd := []string{"sh", "-c", "echo 'version 2 - branch 2' > /app/data.txt && echo 'branch 2 file' > /app/branch2.txt"}
	branch2Output, err := ph.Exec(provider.AsyncExecOpts{SrcSnapToken: baseSnapToken, Command: branch2Cmd})
	require.NoError(t, err, "Failed to create branch 2")
	branch2Token := branch2Output.NewSnapToken
	defer func() {
		p.Remove(branch2Token)
	}()

	// Create a sub-branch from branch 1
	subBranchCmd := []string{"sh", "-c", "echo 'version 3 - subbranch' > /app/data.txt && echo 'subbranch file' > /app/subbranch.txt"}
	subBranchOutput, err := ph.Exec(provider.AsyncExecOpts{SrcSnapToken: branch1Token, Command: subBranchCmd})
	require.NoError(t, err, "Failed to create sub-branch")
	subBranchToken := subBranchOutput.NewSnapToken
	defer func() {
		p.Remove(subBranchToken)
	}()

	// Helper function to verify the state of each branch
	verifyBranchState := func(snapToken, expectedVersion string, expectedFiles, unexpectedFiles []string) {
		// Verify data.txt content
		catCmd := []string{"cat", "/app/data.txt"}
		catOutput, err := ph.ExecReadOnly(provider.AsyncExecOpts{SrcSnapToken: snapToken, Command: catCmd})
		require.NoError(t, err, "Failed to cat data file in %s", snapToken)
		require.Contains(t, catOutput.Stdout, expectedVersion, "Data file in %s should contain '%s'", snapToken, expectedVersion)

		// Verify expected files exist
		for _, file := range expectedFiles {
			fileCheckCmd := []string{"test", "-f", "/app/" + file}
			r, err := ph.ExecReadOnly(provider.AsyncExecOpts{SrcSnapToken: snapToken, Command: fileCheckCmd})
			require.NoError(t, err)
			require.Equal(t, r.ExitCode, 0, "File %s should exist in %s", file, snapToken)
		}

		// Verify unexpected files do not exist
		for _, file := range unexpectedFiles {
			fileCheckCmd := []string{"test", "-f", "/app/" + file}
			r, err := ph.ExecReadOnly(provider.AsyncExecOpts{SrcSnapToken: snapToken, Command: fileCheckCmd})
			require.NoError(t, err)
			require.NotEqual(t, r.ExitCode, 0, "File %s should not exist in %s", file, snapToken)
		}
	}

	// Check base snap: should only have the initial file
	verifyBranchState(baseSnapToken, "version 1", []string{"data.txt"}, []string{"branch1.txt", "branch2.txt", "subbranch.txt"})

	// Check branch 1: should have its own changes, but not from branch 2 or its own sub-branch
	verifyBranchState(branch1Token, "version 2 - branch 1", []string{"data.txt", "branch1.txt"}, []string{"branch2.txt", "subbranch.txt"})

	// Check branch 2: should have its own changes, but not from branch 1 or the sub-branch
	verifyBranchState(branch2Token, "version 2 - branch 2", []string{"data.txt", "branch2.txt"}, []string{"branch1.txt", "subbranch.txt"})

	// Check sub-branch: should inherit from branch 1 and have its own changes
	verifyBranchState(subBranchToken, "version 3 - subbranch", []string{"data.txt", "branch1.txt", "subbranch.txt"}, []string{"branch2.txt"})
}
