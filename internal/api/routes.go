package api

import (
	"errors"
	"net/http"

	"github.com/caarlos0/httperr"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

func errHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Next()
		err := c.Errors.Last()
		if err == nil {
			return
		}
		zap.L().Error("API error", zap.Error(err))
		herr := httperr.Error{}
		if errors.As(err, &herr) {
			c.JSO<PERSON>(herr.Status, gin.H{"error": herr.Error()})
		} else {
			c.JSO<PERSON>(http.StatusInternalServerError, gin.H{"error": "internal server error"})
		}
	}
}

// SetupRoutes sets up the API routes.
func SetupRoutes(r *gin.Engine, handler *Handler) {
	r.Use(errHandler())

	r.GET("/health", func(c *gin.Context) {
		c.<PERSON>(200, gin.H{"status": "ok"})
	})

	api := r.Group("/api/v1")
	forest := api.Group("/forest/:forestID")
	{
		snap := forest.Group("/snap/:snapID")
		{
			snap.POST("/exec", handler.ExecSnap)
			snap.GET("", handler.GetSnap)
			snap.GET("/artifacts/output", handler.GetSnapOutput)
			// snap.GET("/tree", handler.GetSnapTree)
			// snap.GET("/leaves", handler.GetSnapLeaves)
		}

		forest.POST("/burn", handler.BurnForest)
	}

	seedSnap := api.Group("/seed_snap")
	{
		seedSnap.POST("/import", handler.ImportSeedSnap)
	}
}
