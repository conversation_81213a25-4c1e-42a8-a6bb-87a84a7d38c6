package local

import (
	"context"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"time"

	"github.com/breezewish/pantheon-backend/internal/config"
	"github.com/breezewish/pantheon-backend/internal/provider"
	"github.com/docker/docker/api/types/container"
	"github.com/docker/docker/api/types/image"
	"github.com/docker/docker/client"
	"github.com/docker/docker/pkg/stdcopy"
	"go.uber.org/atomic"
)

// LocalProvider is the Docker-based implementation of the Provider interface.
type LocalProvider struct {
	client *client.Client

	logDir string
}

var _ provider.Provider = (*LocalProvider)(nil)

// NewLocalProvider creates a new LocalProvider instance.
func NewLocalProvider(config *config.Config) (*LocalProvider, error) {
	logDir := filepath.Join(config.Storage.DataDir, "localProvider/logs")
	if err := os.MkdirAll(logDir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create log directory %s: %w", logDir, err)
	}

	dockerHost, err := extractDockerHost(context.Background())
	if err != nil {
		return nil, fmt.Errorf("failed to find Docker: %w", err)
	}

	cli, err := client.NewClientWithOpts(
		client.FromEnv,
		client.WithAPIVersionNegotiation(),
		client.WithHost(dockerHost),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create connect Docker client: %w", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	_, err = cli.Ping(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to create connect Docker client: %w", err)
	}
	return &LocalProvider{
		client: cli,
		logDir: logDir,
	}, nil
}

// Import creates a base snapshot from a given image and returns its token.
func (p *LocalProvider) Import(baseImage string) (string, error) {
	ctx := context.Background()

	// Pull the image
	out, err := p.client.ImagePull(ctx, baseImage, image.PullOptions{})
	if err != nil {
		return "", fmt.Errorf("failed to pull Docker image %s: %w", baseImage, err)
	}
	defer out.Close()

	// Wait for the pull to complete
	io.Copy(io.Discard, out)

	// Get the image ID
	inspect, err := p.client.ImageInspect(ctx, baseImage)
	if err != nil {
		return "", fmt.Errorf("failed to inspect Docker image %s: %w", baseImage, err)
	}

	return inspect.ID, nil
}

// Exec runs a command on a source snapshot and creates a new one.
func (p *LocalProvider) AsyncExec(opts provider.AsyncExecOpts) (*provider.AsyncExecResult, error) {
	ctx := context.Background()

	// Convert the envs map to a slice of strings in the format "KEY=VALUE"
	envSlice := []string{}
	for k, v := range opts.Envs {
		envSlice = append(envSlice, fmt.Sprintf("%s=%s", k, v))
	}

	containerConfig := &container.Config{
		Image:        opts.SrcSnapToken,
		Cmd:          opts.Command,
		WorkingDir:   opts.WorkDir,
		Env:          envSlice,
		AttachStdout: true,
		AttachStderr: true,
	}

	hostConfig := &container.HostConfig{
		AutoRemove: opts.SkipCommit,
	}

	resp, err := p.client.ContainerCreate(ctx, containerConfig, hostConfig, nil, nil, "")
	if err != nil {
		return nil, fmt.Errorf("failed to create container from image %s: %w", opts.SrcSnapToken, err)
	}
	containerID := resp.ID

	cleanup := func() {
		stopTimeout := 0
		p.client.ContainerStop(context.Background(), containerID, container.StopOptions{Timeout: &stopTimeout})
		p.client.ContainerRemove(context.Background(), containerID, container.RemoveOptions{Force: true})
	}

	attachResp, err := p.client.ContainerAttach(ctx, containerID, container.AttachOptions{
		Stream: true,
		Stdout: true,
		Stderr: true,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to attach to container %s: %w", containerID, err)
	}

	// Start the container
	if err := p.client.ContainerStart(ctx, containerID, container.StartOptions{}); err != nil {
		cleanup()
		return nil, fmt.Errorf("failed to start container %s: %w", containerID, err)
	}

	stdoutPath := filepath.Join(p.logDir, fmt.Sprintf("%s-stdout.log", containerID))
	stderrPath := filepath.Join(p.logDir, fmt.Sprintf("%s-stderr.log", containerID))
	stdoutFile, _ := os.Create(stdoutPath)
	stderrFile, _ := os.Create(stderrPath)

	exitCode := atomic.NewInt32(0)
	newSnapToken := atomic.NewString("")
	finishCh := make(chan error, 1)

	go func() {
		_, err := stdcopy.StdCopy(stdoutFile, stderrFile, attachResp.Reader)
		if err != nil {
			fmt.Printf("Error demultiplexing Docker exec stream: %v\n", err)
		}
		stdoutFile.Close()
		stderrFile.Close()

		inspectResp, err := p.client.ContainerInspect(ctx, containerID)
		if err != nil {
			cleanup()
			finishCh <- fmt.Errorf("failed to inspect container: %w", err)
			return
		}

		// Force stop the container before commit.
		stopTimeout := 0
		p.client.ContainerStop(context.Background(), containerID, container.StopOptions{Timeout: &stopTimeout})

		exitCode.Store(int32(inspectResp.State.ExitCode))

		if opts.SkipCommit {
			cleanup()
			finishCh <- nil
			return
		}

		commitResp, err := p.client.ContainerCommit(ctx, containerID, container.CommitOptions{})
		if err != nil {
			cleanup()
			finishCh <- fmt.Errorf("failed to commit container %s: %w", containerID, err)
			return
		}

		p.client.ContainerRemove(context.Background(), containerID, container.RemoveOptions{Force: true})

		newSnapToken.Store(commitResp.ID)
		finishCh <- nil
	}()

	return &provider.AsyncExecResult{
		StdoutToken:  stdoutPath,
		StderrToken:  stderrPath,
		FinishCh:     finishCh,
		ExitCode:     exitCode,
		NewSnapToken: newSnapToken,
	}, nil
}

// Remove removes a snapshot managed by the provider.
func (p *LocalProvider) Remove(token string) error {
	ctx := context.Background()
	_, err := p.client.ImageRemove(ctx, token, image.RemoveOptions{})
	if err != nil {
		return fmt.Errorf("failed to remove image %s: %w", token, err)
	}
	return nil
}

// GetStderr retrieves the stderr log for a given execution.
func (p *LocalProvider) GetStderr(stdErrToken string) (string, error) {
	data, err := os.ReadFile(stdErrToken)
	if err != nil {
		return "", fmt.Errorf("failed to read stderr log %s: %w", stdErrToken, err)
	}
	return string(data), nil
}

// GetStdout retrieves the stdout log for a given execution.
func (p *LocalProvider) GetStdout(stdOutToken string) (string, error) {
	data, err := os.ReadFile(stdOutToken)
	if err != nil {
		return "", fmt.Errorf("failed to read stdout log %s: %w", stdOutToken, err)
	}
	return string(data), nil
}
