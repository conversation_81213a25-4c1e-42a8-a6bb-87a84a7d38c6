package api

import (
	"net/http"

	"github.com/breezewish/pantheon-backend/internal/service"
	"github.com/gin-gonic/gin"
)

// Handler contains all HTTP handlers.
type Handler struct {
	snapService *service.SnapService
}

// NewHandler creates a new Handler.
func NewHandler(snapService *service.SnapService) *Handler {
	return &Handler{snapService: snapService}
}

// ExecSnap handles the execution of a command on a snap. This API does not wait for the execution
// to finish. You should pull the status of the execution separately by just GetSnap endpoint.
func (h *Handler) ExecSnap(c *gin.Context) {
	forestID := c.Param("forestID")
	snapID := c.Param("snapID")

	var req service.ExecRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(err)
		return
	}
	resp, err := h.snapService.Exec(forestID, snapID, req)
	if err != nil {
		c.Error(err)
		return
	}
	c.<PERSON>SO<PERSON>(http.StatusOK, resp)
}

// GetSnap handles getting a snap by ID.
func (h *Handler) GetSnap(c *gin.Context) {
	forestID := c.Param("forestID")
	snapID := c.Param("snapID")

	snap, err := h.snapService.GetSnap(forestID, snapID)
	if err != nil {
		c.Error(err)
		return
	}
	c.JSON(http.StatusOK, snap)
}

// GetSnapOutput handles getting the output of a snap.
func (h *Handler) GetSnapOutput(c *gin.Context) {
	forestID := c.Param("forestID")
	snapID := c.Param("snapID")

	output, err := h.snapService.GetSnapOutput(forestID, snapID)
	if err != nil {
		c.Error(err)
		return
	}

	c.String(http.StatusOK, output)
}

// BurnForest handles burning a forest.
func (h *Handler) BurnForest(c *gin.Context) {
	forestID := c.Param("forestID")

	if err := h.snapService.BurnForest(forestID); err != nil {
		c.Error(err)
		return
	}

	c.Status(http.StatusNoContent)
}

// ImportSeedSnap handles importing a seed snap.
func (h *Handler) ImportSeedSnap(c *gin.Context) {
	var req service.ImportSeedRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(err)
		return
	}
	resp, err := h.snapService.ImportSeedSnap(req)
	if err != nil {
		c.Error(err)
		return
	}
	c.JSON(http.StatusCreated, resp)
}
